import { Wifi, WifiOff, Loader } from 'lucide-react';

/**
 * Composant indicateur de statut SSE
 * @param {string} connectionStatus - Statut de la connexion ('connected', 'connecting', 'disconnected', 'error', 'failed')
 * @param {function} onReconnect - Fonction de reconnexion
 */
export function SSEStatusIndicator({ connectionStatus, onReconnect }) {
  const getStatusConfig = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          color: 'bg-green-500',
          icon: Wifi,
          text: 'Temps réel actif',
          textColor: 'text-green-600'
        };
      case 'connecting':
        return {
          color: 'bg-yellow-500',
          icon: Loader,
          text: 'Connexion...',
          textColor: 'text-yellow-600'
        };
      case 'error':
      case 'failed':
        return {
          color: 'bg-red-500',
          icon: WifiOff,
          text: 'Hors ligne',
          textColor: 'text-red-600'
        };
      default:
        return {
          color: 'bg-gray-500',
          icon: WifiOff,
          text: 'Déconnecté',
          textColor: 'text-gray-600'
        };
    }
  };

  const { color, icon: Icon, text, textColor } = getStatusConfig();

  return (
    <div className="flex items-center space-x-2">
      <div className={`w-3 h-3 rounded-full ${color} ${
        connectionStatus === 'connecting' ? 'animate-pulse' : ''
      }`}></div>
      <Icon className={`h-4 w-4 ${textColor}`} />
      <span className={`text-sm ${textColor}`}>
        {text}
      </span>
      {(connectionStatus === 'error' || connectionStatus === 'failed') && onReconnect && (
        <button
          onClick={onReconnect}
          className="text-xs text-blue-600 hover:text-blue-800 underline ml-2"
        >
          Reconnecter
        </button>
      )}
    </div>
  );
}

/**
 * Composant d'alerte pour les problèmes de connexion SSE
 */
export function SSEConnectionAlert({ connectionStatus, onReconnect }) {
  if (connectionStatus === 'connected' || connectionStatus === 'connecting') {
    return null;
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
      <div className="flex">
        <WifiOff className="h-5 w-5 text-yellow-400" />
        <div className="ml-3">
          <h3 className="text-sm font-medium text-yellow-800">
            Connexion temps réel interrompue
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p>
              Les données ne se mettent plus à jour automatiquement. 
              {onReconnect && (
                <>
                  {' '}
                  <button
                    onClick={onReconnect}
                    className="font-medium underline hover:text-yellow-600"
                  >
                    Cliquez ici pour reconnecter
                  </button>
                  .
                </>
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
