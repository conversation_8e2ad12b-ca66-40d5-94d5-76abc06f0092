// Test rapide pour vérifier les imports
console.log('🧪 Test des imports...');

try {
  console.log('1. Test import directApiService...');
  const directServices = await import('./services/directApiService.js');
  console.log('✅ directApiService importé:', Object.keys(directServices));
  
  console.log('2. Test import apiService...');
  const apiServices = await import('./services/apiService.js');
  console.log('✅ apiService importé:', Object.keys(apiServices));
  
  console.log('3. Test des services...');
  if (apiServices.personnelService) {
    console.log('✅ personnelService disponible');
  } else {
    console.log('❌ personnelService manquant');
  }
  
  if (apiServices.badgeService) {
    console.log('✅ badgeService disponible');
  } else {
    console.log('❌ badgeService manquant');
  }
  
  console.log('🎉 Tous les imports fonctionnent !');
  
} catch (error) {
  console.error('❌ Erreur d\'import:', error.message);
  console.error('Stack:', error.stack);
}
