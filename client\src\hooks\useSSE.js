import { useEffect, useRef, useState } from 'react';

/**
 * Hook personnalisé pour gérer les Server-Sent Events
 * @param {string} url - URL de l'endpoint SSE
 * @param {object} options - Options de configuration
 * @returns {object} - État de la connexion et données reçues
 */
export function useSSE(url, options = {}) {
  const {
    onMessage = () => {},
    onError = () => {},
    onOpen = () => {},
    onClose = () => {},
    reconnectInterval = 3000,
    maxReconnectAttempts = 5
  } = options;

  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastEvent, setLastEvent] = useState(null);
  const [error, setError] = useState(null);
  
  const eventSourceRef = useRef(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef(null);

  const connect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    try {
      setConnectionStatus('connecting');
      setError(null);

      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      eventSource.onopen = (event) => {
        console.log('🔗 Connexion SSE établie');
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0;
        onOpen(event);
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          setLastEvent(data);
          onMessage(data);
        } catch (parseError) {
          console.error('Erreur parsing SSE:', parseError);
        }
      };

      eventSource.onerror = (event) => {
        console.error('❌ Erreur SSE:', event);
        setConnectionStatus('error');
        setError('Erreur de connexion SSE');
        onError(event);

        // Tentative de reconnexion
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          console.log(`🔄 Tentative de reconnexion ${reconnectAttemptsRef.current}/${maxReconnectAttempts}`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        } else {
          console.error('❌ Nombre maximum de tentatives de reconnexion atteint');
          setConnectionStatus('failed');
        }
      };

      eventSource.onclose = (event) => {
        console.log('🔌 Connexion SSE fermée');
        setConnectionStatus('disconnected');
        onClose(event);
      };

    } catch (error) {
      console.error('❌ Erreur lors de la création de EventSource:', error);
      setError(error.message);
      setConnectionStatus('failed');
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    setConnectionStatus('disconnected');
  };

  const reconnect = () => {
    reconnectAttemptsRef.current = 0;
    connect();
  };

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [url]);

  return {
    connectionStatus,
    lastEvent,
    error,
    reconnect,
    disconnect
  };
}

/**
 * Hook spécialisé pour les événements de l'application de contrôle d'accès
 */
export function useAccessControlSSE() {
  const [personnelUpdates, setPersonnelUpdates] = useState(null);
  const [badgeUpdates, setBadgeUpdates] = useState(null);
  const [passageUpdates, setPassageUpdates] = useState(null);
  const [attributionUpdates, setAttributionUpdates] = useState(null);
  const [statsUpdates, setStatsUpdates] = useState(null);

  const handleMessage = (event) => {
    switch (event.type) {
      case 'personnel_update':
        setPersonnelUpdates(event.data);
        break;
      case 'badge_update':
        setBadgeUpdates(event.data);
        break;
      case 'passage_update':
        setPassageUpdates(event.data);
        break;
      case 'attribution_update':
        setAttributionUpdates(event.data);
        break;
      case 'stats_update':
        setStatsUpdates(event.data);
        break;
      case 'connection':
        console.log('✅ Connexion SSE établie:', event.message);
        break;
      case 'heartbeat':
        // Heartbeat silencieux
        break;
      default:
        console.log('📡 Événement SSE reçu:', event);
    }
  };

  const sseState = useSSE('http://localhost:3001/api/sse/events', {
    onMessage: handleMessage,
    onError: (error) => console.error('Erreur SSE:', error),
    onOpen: () => console.log('Connexion SSE ouverte'),
    onClose: () => console.log('Connexion SSE fermée')
  });

  return {
    ...sseState,
    personnelUpdates,
    badgeUpdates,
    passageUpdates,
    attributionUpdates,
    statsUpdates,
    clearPersonnelUpdates: () => setPersonnelUpdates(null),
    clearBadgeUpdates: () => setBadgeUpdates(null),
    clearPassageUpdates: () => setPassageUpdates(null),
    clearAttributionUpdates: () => setAttributionUpdates(null),
    clearStatsUpdates: () => setStatsUpdates(null)
  };
}
