const express = require('express');
const router = express.Router();

// Store des connexions SSE actives
const sseConnections = new Set();

/**
 * @route GET /api/sse/events
 * @desc Endpoint pour les Server-Sent Events
 * @access Public
 */
router.get('/events', (req, res) => {
  // Configuration des headers SSE
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  // Envoyer un événement de connexion
  res.write(`data: ${JSON.stringify({
    type: 'connection',
    message: 'Connexion SSE établie',
    timestamp: new Date().toISOString()
  })}\n\n`);

  // Ajouter la connexion au store
  sseConnections.add(res);

  // Heartbeat pour maintenir la connexion
  const heartbeat = setInterval(() => {
    res.write(`data: ${JSON.stringify({
      type: 'heartbeat',
      timestamp: new Date().toISOString()
    })}\n\n`);
  }, 30000); // Heartbeat toutes les 30 secondes

  // Nettoyage lors de la fermeture de la connexion
  req.on('close', () => {
    clearInterval(heartbeat);
    sseConnections.delete(res);
    console.log('Connexion SSE fermée');
  });

  req.on('error', () => {
    clearInterval(heartbeat);
    sseConnections.delete(res);
  });
});

/**
 * Fonction pour diffuser un événement à toutes les connexions actives
 * @param {string} type - Type d'événement
 * @param {object} data - Données à envoyer
 */
function broadcastEvent(type, data) {
  const event = {
    type,
    data,
    timestamp: new Date().toISOString()
  };

  const eventString = `data: ${JSON.stringify(event)}\n\n`;

  // Envoyer à toutes les connexions actives
  sseConnections.forEach(res => {
    try {
      res.write(eventString);
    } catch (error) {
      // Supprimer les connexions fermées
      sseConnections.delete(res);
    }
  });

  console.log(`📡 Événement SSE diffusé: ${type}`, data);
}

/**
 * Fonction pour diffuser les mises à jour de personnel
 */
function broadcastPersonnelUpdate(action, personnel) {
  broadcastEvent('personnel_update', {
    action, // 'created', 'updated', 'deleted'
    personnel
  });
}

/**
 * Fonction pour diffuser les mises à jour de badges
 */
function broadcastBadgeUpdate(action, badge) {
  broadcastEvent('badge_update', {
    action, // 'created', 'updated', 'deleted', 'status_changed'
    badge
  });
}

/**
 * Fonction pour diffuser les nouveaux passages
 */
function broadcastPassageUpdate(passage) {
  broadcastEvent('passage_update', {
    action: 'new_passage',
    passage
  });
}

/**
 * Fonction pour diffuser les mises à jour d'attributions
 */
function broadcastAttributionUpdate(action, attribution) {
  broadcastEvent('attribution_update', {
    action, // 'created', 'closed'
    attribution
  });
}

/**
 * Fonction pour diffuser les mises à jour de statistiques
 */
function broadcastStatsUpdate(stats) {
  broadcastEvent('stats_update', {
    action: 'updated',
    stats
  });
}

module.exports = {
  router,
  broadcastEvent,
  broadcastPersonnelUpdate,
  broadcastBadgeUpdate,
  broadcastPassageUpdate,
  broadcastAttributionUpdate,
  broadcastStatsUpdate,
  getActiveConnections: () => sseConnections.size
};
