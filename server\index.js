const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import de la configuration de base de données et des modèles
const { testConnection } = require('./config/database');
const models = require('./models');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware de sécurité
app.use(helmet());
app.use(compression());

// Configuration CORS
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173'  // Vite dev server
  ],
  credentials: true
}));

// Rate limiting - Configuration plus permissive pour le développement
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 1000, // 1000 requêtes par 15 min en dev
  message: 'Trop de requêtes depuis cette IP, veuillez réessayer plus tard.',
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Appliquer le rate limiting seulement en production
if (process.env.NODE_ENV === 'production') {
  app.use('/api/', limiter);
}

// Middleware de parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Logging
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

// Routes de base
app.get('/', (req, res) => {
  res.json({
    message: 'API Système de Contrôle d\'Accès Militaire',
    version: '1.0.0',
    status: 'active'
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Routes API
app.use('/api/personnel', require('./routes/personnel'));
app.use('/api/reference', require('./routes/reference'));
app.use('/api/badges', require('./routes/badges'));
app.use('/api/statistiques', require('./routes/statistiques'));
app.use('/api/passages', require('./routes/passages'));
app.use('/api/sse', require('./routes/sse').router);
// app.use('/api/auth', require('./routes/auth'));

// Middleware de gestion d'erreurs
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Erreur interne du serveur',
    error: process.env.NODE_ENV === 'development' ? err.message : {}
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    message: 'Route non trouvée'
  });
});

// Démarrage du serveur avec initialisation de la base de données
const startServer = async () => {
  try {
    // Test de connexion à la base de données
    await testConnection();

    // Synchronisation des modèles (optionnel en développement)
    if (process.env.NODE_ENV === 'development') {
      await models.sequelize.sync({ alter: false });
      console.log('📊 Modèles synchronisés avec la base de données');
    }

    // Démarrage du serveur
    app.listen(PORT, () => {
      console.log(`🚀 Serveur démarré sur le port ${PORT}`);
      console.log(`📊 Environnement: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 URL: http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('❌ Erreur lors du démarrage du serveur:', error);
    process.exit(1);
  }
};

startServer();

module.exports = app;
