import { useState, useEffect } from 'react';
import { CreditCard, Plus, Power, PowerOff, Edit, Trash2 } from 'lucide-react';
import { badgeService } from '../services/directApiService';
import { Card, CardHeader, CardContent, CardTitle, Modal, DataTable, Alert } from '../components/ui';
import { BadgeForm } from '../components/BadgeForm';

function BadgeManagement() {
  const [badges, setBadges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedBadge, setSelectedBadge] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');

  const fetchBadges = async () => {
    try {
      setLoading(true);
      const response = await badgeService.getManagementList({
        statut: filterStatus === 'all' ? undefined : filterStatus
      });
      setBadges(response.badges || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des badges');
      console.error('Error fetching badges:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBadges();

    // Auto-refresh toutes les 5 secondes
    const interval = setInterval(fetchBadges, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleAddBadge = () => {
    setSelectedBadge(null);
    setShowModal(true);
  };

  const handleEditBadge = (badge) => {
    setSelectedBadge(badge);
    setShowModal(true);
  };

  const handleToggleBadgeStatus = async (badge) => {
    try {
      await badgeService.toggleStatus(badge.id);
      fetchBadges();
    } catch {
      setError('Erreur lors de la modification du statut');
    }
  };

  const handleDeleteBadge = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce badge ?')) {
      try {
        await badgeService.delete(id);
        fetchBadges();
      } catch {
        setError('Erreur lors de la suppression');
      }
    }
  };

  const handleFormSuccess = () => {
    setShowModal(false);
    fetchBadges();
  };

  const filteredBadges = badges.filter(badge => {
    if (filterStatus === 'all') return true;
    return badge.statut === filterStatus;
  });

  const columns = [
    {
      header: 'Numéro',
      accessor: 'numero',
      render: (badge) => (
        <div className="flex items-center space-x-2">
          <CreditCard className="h-4 w-4 text-gray-400" />
          <span className="font-medium">#{badge.numero}</span>
        </div>
      )
    },
    {
      header: 'Type',
      accessor: 'type',
      render: (badge) => {
        const typeLabels = {
          militaire_interne: 'Militaire Interne',
          visiteur: 'Visiteur'
        };
        const typeColors = {
          militaire_interne: 'bg-blue-100 text-blue-800',
          visiteur: 'bg-orange-100 text-orange-800'
        };
        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${typeColors[badge.type]}`}>
            {typeLabels[badge.type]}
          </span>
        );
      }
    },
    {
      header: 'Statut',
      accessor: 'statut',
      render: (badge) => {
        const statusLabels = {
          actif: 'Actif',
          desactive: 'Désactivé',
          attribue: 'Attribué',
          expire: 'Expiré'
        };
        const statusColors = {
          actif: 'bg-green-100 text-green-800',
          desactive: 'bg-gray-100 text-gray-800',
          attribue: 'bg-blue-100 text-blue-800',
          expire: 'bg-red-100 text-red-800'
        };
        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColors[badge.statut]}`}>
            {statusLabels[badge.statut]}
          </span>
        );
      }
    },
    {
      header: 'Personnel Attribué',
      render: (badge) => {
        if (badge.personnel) {
          return (
            <div>
              <div className="font-medium text-gray-900">
                {badge.personnel.nom} {badge.personnel.prenom}
              </div>
              <div className="text-sm text-gray-500">
                {badge.personnel.matricule || badge.personnel.cin}
              </div>
            </div>
          );
        }
        return <span className="text-gray-400">Non attribué</span>;
      }
    },
    {
      header: 'Date Création',
      accessor: 'date_creation',
      render: (badge) => new Date(badge.date_creation).toLocaleDateString('fr-FR')
    },
    {
      header: 'Actions',
      render: (badge) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleToggleBadgeStatus(badge)}
            className={`${
              badge.statut === 'actif' ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'
            }`}
            title={badge.statut === 'actif' ? 'Désactiver' : 'Activer'}
          >
            {badge.statut === 'actif' ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />}
          </button>
          <button
            onClick={() => handleEditBadge(badge)}
            className="text-blue-600 hover:text-blue-800"
            title="Modifier"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleDeleteBadge(badge.id)}
            className="text-red-600 hover:text-red-800"
            title="Supprimer"
            disabled={badge.statut === 'attribue'}
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  const badgeStats = {
    total: badges.length,
    actifs: badges.filter(b => b.statut === 'actif').length,
    attribues: badges.filter(b => b.statut === 'attribue').length,
    desactives: badges.filter(b => b.statut === 'desactive').length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Gestion des Badges</h1>
        <button
          onClick={handleAddBadge}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Nouveau Badge</span>
        </button>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistiques des badges */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Badges</p>
                <p className="text-2xl font-bold text-gray-800">{badgeStats.total}</p>
              </div>
              <CreditCard className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Actifs</p>
                <p className="text-2xl font-bold text-green-600">{badgeStats.actifs}</p>
              </div>
              <Power className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Attribués</p>
                <p className="text-2xl font-bold text-blue-600">{badgeStats.attribues}</p>
              </div>
              <CreditCard className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Désactivés</p>
                <p className="text-2xl font-bold text-gray-600">{badgeStats.desactives}</p>
              </div>
              <PowerOff className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Liste des Badges</CardTitle>
            <div className="flex items-center space-x-4">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">Tous les statuts</option>
                <option value="actif">Actifs</option>
                <option value="attribue">Attribués</option>
                <option value="desactive">Désactivés</option>
                <option value="expire">Expirés</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            data={filteredBadges}
            columns={columns}
            searchable={true}
            sortable={true}
            pagination={true}
            pageSize={10}
          />
        </CardContent>
      </Card>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={selectedBadge ? 'Modifier Badge' : 'Nouveau Badge'}
        size="md"
      >
        <BadgeForm
          badge={selectedBadge}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowModal(false)}
        />
      </Modal>
    </div>
  );
}

export default BadgeManagement;
