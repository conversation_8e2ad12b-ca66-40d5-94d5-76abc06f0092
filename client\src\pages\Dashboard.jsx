import { useState, useEffect } from 'react';
import { personnelService, badgeService, passageService } from "../services/directApiService";
import { StatCards } from "../components/StatCards";
import { RealTimeHistory } from "../components/RealTimeHistory";
import { AccessLogTable } from "../components/AccessLogTable";
import { QuickActions } from "../components/QuickActions";
import { Alert } from "../components/ui";

function Dashboard() {
  const [personnels, setPersonnels] = useState([]);
  const [passages, setPassages] = useState([]);
  const [badgesActifs, setBadgesActifs] = useState(0);
  const [badgesDisponibles, setBadgesDisponibles] = useState(0);
  const [effectifs, setEffectifs] = useState({
    militairesInternes: 0,
    militairesExternes: 0,
    civils: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les données en parallèle avec les nouvelles routes spécifiques
      const [personnelsData, passagesData, badgesStats] = await Promise.all([
        personnelService.getDashboardSummary(),
        passageService.getDashboardRecent(10),
        badgeService.getDashboardStats()
      ]);

      setPersonnels(personnelsData);
      setPassages(passagesData);

      // Calculer les effectifs par type (données déjà formatées par l'API)
      const effectifsCalcules = personnelsData.reduce((acc, personnel) => {
        if (personnel.type === 'militaire_interne') acc.militairesInternes++;
        else if (personnel.type === 'militaire_externe') acc.militairesExternes++;
        else if (personnel.type === 'civil_externe') acc.civils++;
        return acc;
      }, { militairesInternes: 0, militairesExternes: 0, civils: 0 });
      setEffectifs(effectifsCalcules);

      // Utiliser les statistiques directement de l'API
      setBadgesActifs(badgesStats.attribues || 0);
      setBadgesDisponibles(badgesStats.disponibles || 0);

    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);

      // Gestion spécifique du rate limiting
      if (err.response?.status === 429) {
        setError('Trop de requêtes. Veuillez patienter quelques minutes...');
      } else {
        setError('Erreur lors du chargement des données du dashboard');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();

    // Auto-refresh toutes les 5 secondes
    const interval = setInterval(fetchData, 5000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement du tableau de bord...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Tableau de Bord</h1>
        <div className="text-sm text-gray-500">
          Mise à jour automatique toutes les 5 secondes
        </div>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      <StatCards
        passages={passages.length}
        badgesActifs={badgesActifs}
        badgesDisponibles={badgesDisponibles}
        personnels={personnels.length}
        effectifs={effectifs}
      />

      <QuickActions onSuccess={fetchData} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <RealTimeHistory personnels={personnels} />
        <AccessLogTable passages={passages.slice(0, 10)} />
      </div>
    </div>
  );
}

export default Dashboard;