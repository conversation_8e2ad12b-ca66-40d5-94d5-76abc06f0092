import { useState, useEffect } from 'react';
import { personnelService, badgeService, passageService } from "../services/directApiService";
import { StatCards } from "../components/StatCards";
import { RealTimeHistory } from "../components/RealTimeHistory";
import { AccessLogTable } from "../components/AccessLogTable";
import { QuickActions } from "../components/QuickActions";
import { Alert } from "../components/ui";
import { useAccessControlSSE } from '../hooks/useSSE';
import { SSEStatusIndicator, SSEConnectionAlert } from '../components/SSEStatusIndicator';

function Dashboard() {
  const [personnels, setPersonnels] = useState([]);
  const [passages, setPassages] = useState([]);
  const [badgesActifs, setBadgesActifs] = useState(0);
  const [badgesDisponibles, setBadgesDisponibles] = useState(0);
  const [effectifs, setEffectifs] = useState({
    militairesInternes: 0,
    militairesExternes: 0,
    civils: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Hook SSE pour les mises à jour en temps réel
  const {
    connectionStatus,
    personnelUpdates,
    badgeUpdates,
    passageUpdates,
    statsUpdates,
    clearPersonnelUpdates,
    clearBadgeUpdates,
    clearPassageUpdates,
    clearStatsUpdates,
    reconnect
  } = useAccessControlSSE();

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les données en parallèle avec les nouvelles routes spécifiques
      const [personnelsData, passagesData, badgesStats] = await Promise.all([
        personnelService.getDashboardSummary(),
        passageService.getDashboardRecent(10),
        badgeService.getDashboardStats()
      ]);

      setPersonnels(personnelsData);
      setPassages(passagesData);

      // Calculer les effectifs par type (données déjà formatées par l'API)
      const effectifsCalcules = personnelsData.reduce((acc, personnel) => {
        if (personnel.type === 'militaire_interne') acc.militairesInternes++;
        else if (personnel.type === 'militaire_externe') acc.militairesExternes++;
        else if (personnel.type === 'civil_externe') acc.civils++;
        return acc;
      }, { militairesInternes: 0, militairesExternes: 0, civils: 0 });
      setEffectifs(effectifsCalcules);

      // Utiliser les statistiques directement de l'API
      setBadgesActifs(badgesStats.attribues || 0);
      setBadgesDisponibles(badgesStats.disponibles || 0);

    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);

      // Gestion spécifique du rate limiting
      if (err.response?.status === 429) {
        setError('Trop de requêtes. Veuillez patienter quelques minutes...');
      } else {
        setError('Erreur lors du chargement des données du dashboard');
      }
    } finally {
      setLoading(false);
    }
  };

  // Chargement initial des données
  useEffect(() => {
    fetchData();
  }, []);

  // Gestion des mises à jour SSE pour le personnel
  useEffect(() => {
    if (personnelUpdates) {
      console.log('📡 Mise à jour personnel reçue:', personnelUpdates);
      // Recharger les données du personnel
      personnelService.getDashboardSummary().then(data => {
        setPersonnels(data);
        // Recalculer les effectifs
        const effectifsCalcules = data.reduce((acc, p) => {
          if (p.type === 'militaire_interne') acc.militairesInternes++;
          else if (p.type === 'militaire_externe') acc.militairesExternes++;
          else if (p.type === 'civil_externe') acc.civils++;
          return acc;
        }, { militairesInternes: 0, militairesExternes: 0, civils: 0 });
        setEffectifs(effectifsCalcules);
      });
      clearPersonnelUpdates();
    }
  }, [personnelUpdates]);

  // Gestion des mises à jour SSE pour les badges
  useEffect(() => {
    if (badgeUpdates) {
      console.log('📡 Mise à jour badge reçue:', badgeUpdates);
      // Recharger les stats des badges
      badgeService.getDashboardStats().then(stats => {
        setBadgesActifs(stats.actifs);
        setBadgesDisponibles(stats.disponibles);
      });
      clearBadgeUpdates();
    }
  }, [badgeUpdates]);

  // Gestion des mises à jour SSE pour les passages
  useEffect(() => {
    if (passageUpdates) {
      console.log('📡 Nouveau passage reçu:', passageUpdates);
      // Recharger les passages récents
      passageService.getDashboardRecent(10).then(data => {
        setPassages(data);
      });
      clearPassageUpdates();
    }
  }, [passageUpdates]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement du tableau de bord...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Tableau de Bord</h1>
        <SSEStatusIndicator
          connectionStatus={connectionStatus}
          onReconnect={reconnect}
        />
      </div>

      <SSEConnectionAlert
        connectionStatus={connectionStatus}
        onReconnect={reconnect}
      />

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      <StatCards
        passages={passages.length}
        badgesActifs={badgesActifs}
        badgesDisponibles={badgesDisponibles}
        personnels={personnels.length}
        effectifs={effectifs}
      />

      <QuickActions onSuccess={fetchData} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <RealTimeHistory personnels={personnels} />
        <AccessLogTable passages={passages.slice(0, 10)} />
      </div>
    </div>
  );
}

export default Dashboard;