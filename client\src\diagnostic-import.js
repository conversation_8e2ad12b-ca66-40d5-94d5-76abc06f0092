// Diagnostic complet des imports
import fs from 'fs';
import path from 'path';

console.log('🔍 DIAGNOSTIC DES IMPORTS');
console.log('=' .repeat(50));

// Vérifier l'existence des fichiers
const files = [
  'src/services/api.js',
  'src/services/apiService.js', 
  'src/services/directApiService.js'
];

console.log('📁 Vérification des fichiers:');
files.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    const stats = fs.statSync(fullPath);
    console.log(`✅ ${file} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${file} - MANQUANT`);
  }
});

console.log('\n📄 Contenu des fichiers:');

// Vérifier le contenu d'apiService.js
try {
  const apiServicePath = path.join(process.cwd(), 'src/services/apiService.js');
  const content = fs.readFileSync(apiServicePath, 'utf8');
  console.log('📝 apiService.js:');
  console.log(content);
} catch (error) {
  console.log('❌ Erreur lecture apiService.js:', error.message);
}

console.log('\n🧪 Test des imports dynamiques:');

// Test import dynamique
try {
  console.log('Test 1: Import direct de directApiService...');
  const directModule = await import('./services/directApiService.js');
  console.log('✅ directApiService chargé, exports:', Object.keys(directModule));
  
  console.log('Test 2: Import de apiService...');
  const apiModule = await import('./services/apiService.js');
  console.log('✅ apiService chargé, exports:', Object.keys(apiModule));
  
  console.log('Test 3: Vérification des services...');
  if (apiModule.personnelService && typeof apiModule.personnelService.getDashboardSummary === 'function') {
    console.log('✅ personnelService.getDashboardSummary disponible');
  } else {
    console.log('❌ personnelService.getDashboardSummary manquant');
  }
  
} catch (error) {
  console.log('❌ Erreur import:', error.message);
  console.log('Stack:', error.stack);
}

console.log('\n🌐 Test de connectivité API:');

// Test de connectivité
try {
  const response = await fetch('http://localhost:3001/api/reference/all');
  if (response.ok) {
    console.log('✅ Serveur backend accessible');
  } else {
    console.log(`❌ Serveur backend erreur: ${response.status}`);
  }
} catch (error) {
  console.log('❌ Serveur backend non accessible:', error.message);
}

console.log('\n📋 RÉSUMÉ:');
console.log('- Vérifiez que le serveur backend est démarré (npm run dev dans /server)');
console.log('- Redémarrez le serveur frontend (Ctrl+C puis npm run dev dans /client)');
console.log('- Videz le cache du navigateur (Ctrl+Shift+R)');
console.log('- Vérifiez la console du navigateur pour d\'autres erreurs');
