import { useState, useEffect } from 'react';
import { Settings as SettingsIcon, Plus, Edit, Trash2, Save, X } from 'lucide-react';
import { referenceService } from '../services/directApiService';
import { Card, CardHeader, CardContent, CardTitle, Modal, DataTable, Alert } from '../components/ui';

function Settings() {
  const [activeTab, setActiveTab] = useState('unites');
  const [data, setData] = useState({
    unites: [],
    grades: [],
    portes: [],
    typesBadges: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [formData, setFormData] = useState({});

  const tabs = [
    { id: 'unites', label: 'Unités', description: 'Gestion des unités militaires' },
    { id: 'grades', label: 'Grades', description: 'Gestion des grades militaires' },
    { id: 'portes', label: 'Portes', description: 'Gestion des portes d\'accès' },
    { id: 'typesBadges', label: 'Types de Badges', description: 'Configuration des types de badges' }
  ];

  const fetchData = async () => {
    try {
      setLoading(true);
      const allData = await referenceService.getAll();
      setData(allData);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des données');
      console.error('Error fetching data:', err);
      // Données par défaut en cas d'erreur
      setData({
        unites: [],
        grades: [],
        portes: [],
        typesBadges: []
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();

    // Auto-refresh toutes les 5 secondes
    const interval = setInterval(fetchData, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleAdd = () => {
    setSelectedItem(null);
    setFormData(getEmptyFormData(activeTab));
    setShowModal(true);
  };

  const handleEdit = (item) => {
    setSelectedItem(item);
    setFormData(item);
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
      try {
        await referenceService.delete(activeTab.slice(0, -1), id); // Enlever le 's' final
        setSuccess('Élément supprimé avec succès');
        fetchData();
      } catch {
        setError('Erreur lors de la suppression');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const type = activeTab.slice(0, -1); // Enlever le 's' final
      if (selectedItem) {
        await referenceService.update(type, selectedItem.id, formData);
        setSuccess('Élément modifié avec succès');
      } else {
        await referenceService.create(type, formData);
        setSuccess('Élément créé avec succès');
      }
      setShowModal(false);
      fetchData();
    } catch {
      setError('Erreur lors de la sauvegarde');
    }
  };

  const getEmptyFormData = (tab) => {
    switch (tab) {
      case 'unites':
        return { nom: '', description: '', code: '' };
      case 'grades':
        return { nom: '', niveau: '', description: '' };
      case 'portes':
        return { nom: '', localisation: '', type_acces: 'entree', description: '' };
      case 'typesBadges':
        return { nom: '', couleur: '#3B82F6', duree_validite: '', description: '' };
      default:
        return {};
    }
  };

  const getColumns = (tab) => {
    const baseColumns = [
      {
        header: 'Nom',
        accessor: 'nom',
        render: (item) => (
          <div>
            <div className="font-medium text-gray-900">{item.nom}</div>
            {item.code && <div className="text-sm text-gray-500">Code: {item.code}</div>}
          </div>
        )
      }
    ];

    switch (tab) {
      case 'unites':
        return [
          ...baseColumns,
          { header: 'Code', accessor: 'code' },
          { header: 'Description', accessor: 'description' },
          {
            header: 'Actions',
            render: (item) => (
              <div className="flex space-x-2">
                <button onClick={() => handleEdit(item)} className="text-blue-600 hover:text-blue-800">
                  <Edit className="h-4 w-4" />
                </button>
                <button onClick={() => handleDelete(item.id)} className="text-red-600 hover:text-red-800">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            )
          }
        ];
      case 'grades':
        return [
          ...baseColumns,
          { header: 'Niveau', accessor: 'niveau' },
          { header: 'Description', accessor: 'description' },
          {
            header: 'Actions',
            render: (item) => (
              <div className="flex space-x-2">
                <button onClick={() => handleEdit(item)} className="text-blue-600 hover:text-blue-800">
                  <Edit className="h-4 w-4" />
                </button>
                <button onClick={() => handleDelete(item.id)} className="text-red-600 hover:text-red-800">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            )
          }
        ];
      case 'portes':
        return [
          ...baseColumns,
          { header: 'Localisation', accessor: 'localisation' },
          {
            header: 'Type d\'accès',
            accessor: 'type_acces',
            render: (item) => (
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                item.type_acces === 'entree' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
              }`}>
                {item.type_acces === 'entree' ? 'Entrée' : 'Sortie'}
              </span>
            )
          },
          {
            header: 'Actions',
            render: (item) => (
              <div className="flex space-x-2">
                <button onClick={() => handleEdit(item)} className="text-blue-600 hover:text-blue-800">
                  <Edit className="h-4 w-4" />
                </button>
                <button onClick={() => handleDelete(item.id)} className="text-red-600 hover:text-red-800">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            )
          }
        ];
      case 'typesBadges':
        return [
          {
            header: 'Nom',
            accessor: 'nom',
            render: (item) => (
              <div className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded-full border"
                  style={{ backgroundColor: item.couleur }}
                />
                <span className="font-medium text-gray-900">{item.nom}</span>
              </div>
            )
          },
          { header: 'Durée de validité', accessor: 'duree_validite', render: (item) => item.duree_validite ? `${item.duree_validite} jours` : 'Illimitée' },
          { header: 'Description', accessor: 'description' },
          {
            header: 'Actions',
            render: (item) => (
              <div className="flex space-x-2">
                <button onClick={() => handleEdit(item)} className="text-blue-600 hover:text-blue-800">
                  <Edit className="h-4 w-4" />
                </button>
                <button onClick={() => handleDelete(item.id)} className="text-red-600 hover:text-red-800">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            )
          }
        ];
      default:
        return baseColumns;
    }
  };

  const renderForm = () => {
    switch (activeTab) {
      case 'unites':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
              <input
                type="text"
                value={formData.nom || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, nom: e.target.value }))}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Code</label>
              <input
                type="text"
                value={formData.code || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        );
      case 'grades':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
              <input
                type="text"
                value={formData.nom || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, nom: e.target.value }))}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Niveau</label>
              <input
                type="number"
                value={formData.niveau || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, niveau: parseInt(e.target.value) }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        );
      case 'portes':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
              <input
                type="text"
                value={formData.nom || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, nom: e.target.value }))}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Localisation</label>
              <input
                type="text"
                value={formData.localisation || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, localisation: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Type d'accès</label>
              <select
                value={formData.type_acces || 'entree'}
                onChange={(e) => setFormData(prev => ({ ...prev, type_acces: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="entree">Entrée</option>
                <option value="sortie">Sortie</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        );
      case 'typesBadges':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
              <input
                type="text"
                value={formData.nom || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, nom: e.target.value }))}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Couleur</label>
              <input
                type="color"
                value={formData.couleur || '#3B82F6'}
                onChange={(e) => setFormData(prev => ({ ...prev, couleur: e.target.value }))}
                className="w-full h-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Durée de validité (jours)</label>
              <input
                type="number"
                value={formData.duree_validite || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, duree_validite: parseInt(e.target.value) }))}
                placeholder="Laisser vide pour illimitée"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement...</div>
      </div>
    );
  }

  const currentTab = tabs.find(tab => tab.id === activeTab);
  const currentData = data[activeTab] || [];

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Paramètres</h1>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-6" dismissible onDismiss={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Onglets */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <SettingsIcon className="h-5 w-5" />
                <span>{currentTab?.label}</span>
              </CardTitle>
              <p className="text-sm text-gray-500 mt-1">{currentTab?.description}</p>
            </div>
            <button
              onClick={handleAdd}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Ajouter</span>
            </button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            data={currentData}
            columns={getColumns(activeTab)}
            searchable={true}
            sortable={true}
            pagination={true}
            pageSize={10}
          />
        </CardContent>
      </Card>

      {/* Modal de formulaire */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={`${selectedItem ? 'Modifier' : 'Ajouter'} ${currentTab?.label.slice(0, -1)}`}
        size="md"
      >
        <form onSubmit={handleSubmit}>
          {renderForm()}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
            <button
              type="button"
              onClick={() => setShowModal(false)}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center space-x-2"
            >
              <X className="h-4 w-4" />
              <span>Annuler</span>
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{selectedItem ? 'Modifier' : 'Créer'}</span>
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
}

export default Settings;
